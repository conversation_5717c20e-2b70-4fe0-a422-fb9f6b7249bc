version: 0.1

default:
  global:
    parameters:
      stack_name: "xl4_delta_server"
  build:
    parameters:
      parallel: true
  validate:
    parameters:
      lint: true
  deploy:
    parameters:
      capabilities: "CAPABILITY_IAM"
      confirm_changeset: true
      resolve_s3: true
      resolve_image_repos: true
      s3_prefix: "delta-server"
      region: "us-east-1"
      parameter_overrides:
        # Environment-specific database configuration
        - ParameterKey=DbHost,ParameterValue="xl4-common-rds.c1ivsvco3of0.us-east-1.rds.amazonaws.com"
        - ParameterKey=DbName,ParameterValue="delta_server_default"
        - ParameterKey=DbPassword,ParameterValue="snapstore"
        - ParameterKey=DbUserName,ParameterValue="snapstore"
        # Environment-specific infrastructure
        - ParameterKey=DockerImageUrl,ParameterValue="588846411770.dkr.ecr.us-east-1.amazonaws.com/deltaserver-executor/esdiff-demo-delta-server:latest"
        - ParameterKey=SubnetIds,ParameterValue="subnet-0e184366ab69c3cca,subnet-0ea02b0d81884d67a,subnet-0ffc9784b26bbcca1"
        - ParameterKey=SecurityGroupIds,ParameterValue="sg-0c79a38a3755587cb"

deltaserverguruqa:
  build:
    parameters:
      parallel: true
  validate:
    parameters:
      lint: true
  deploy:
    parameters:
      stack_name: "delta-server-guru-qa"
      capabilities: "CAPABILITY_IAM"
      confirm_changeset: true
      resolve_s3: true
      resolve_image_repos: true
      region: "us-east-1"
      parameter_overrides:
        # Network Configuration (shared by Lambda and ECS)
        - ParameterKey=SubnetIds,ParameterValue="subnet-0a3b50f4fb30ee17e,subnet-031bc4fdf1094d9d8,subnet-02ee9e031ea14a8be"
        - ParameterKey=SecurityGroupIds,ParameterValue="sg-0ed882bb814abedf9"
        - ParameterKey=LbSubnetIds,ParameterValue="subnet-04d478f4dba2c087f,subnet-036fbaab7816b1b86,subnet-0a6cd5622b3dc5843"
        - ParameterKey=LbSecurityGroupIds,ParameterValue="sg-0f1f88bf2d01ca766"
        - ParameterKey=AcmCertificateArn,ParameterValue="arn:aws:acm:us-east-1:588846411770:certificate/bc58cb53-ed93-4f4a-8394-e9c9e5c800d2"
        - ParameterKey=TrustStoreArn,ParameterValue="arn:aws:elasticloadbalancing:us-east-1:588846411770:truststore/excelfore-acmpca-rootca/ef02d6c51e33391e"
        - ParameterKey=LambdaFunctionName,ParameterValue="DeltaServerGuruQa"
        - ParameterKey=LambdaEventRecorderFunctionName,ParameterValue="DeltaServerGuruQaEventRecorder"
        - ParameterKey=LambdaS3FileTrackerFunctionName,ParameterValue="DeltaServerGuruQaS3FileTracker"
        - ParameterKey=TargetGroupName,ParameterValue="DeltaServerGuruQaTargetGroup"
        - ParameterKey=LoadBalancerName,ParameterValue="DeltaServerGuruQaLoadBalancer"
        - ParameterKey=IamPolicyName,ParameterValue="DeltaServerGuruQaIamPolicyName"
        # Environment-specific database configuration
        - ParameterKey=DeltaServerRdsName,ParameterValue="guru-delta-server-sam"
        - ParameterKey=DbHost,ParameterValue="guru-delta-server-sam.c0njbmfj0k8p.us-east-1.rds.amazonaws.com"
        - ParameterKey=DbName,ParameterValue="snapstore"
        - ParameterKey=DbPassword,ParameterValue="snapstore"
        - ParameterKey=DbUserName,ParameterValue="snapstore"
        # Environment-specific infrastructure
        - ParameterKey=DockerImageUrl,ParameterValue="588846411770.dkr.ecr.us-east-1.amazonaws.com/deltaserver-executor:esdiff-demo-delta-server"
        # Environment-specific Bucket Name
        - ParameterKey=DownloadS3Bucket,ParameterValue="esdiff-test-s33"
        - ParameterKey=S3TrackingBucket,ParameterValue="esdiff-test-s33"
