# S3 File Tracker Lambda

This is an **event-driven Lambda function** that automatically tracks all file operations (CRUD) in configured S3 buckets and stores metadata including file fingerprints in the database. It gets triggered by S3 bucket events through EventBridge/CloudWatch Events.

## Architecture

- **Event-Driven**: Triggered by S3 bucket events (not API requests)
- **Automatic Processing**: No manual invocation required
- **Background Operation**: Runs asynchronously when files are added/modified/deleted

## Features

- **Automatic File Tracking**: Responds to S3 bucket events (Create, Update, Delete)
- **File Fingerprinting**: Generates unique fingerprints for file content identification
- **Metadata Storage**: Stores comprehensive file information in database
- **Event Processing**: Handles S3 event records from EventBridge
- **Error Handling**: Robust error handling for failed operations

## Database Schema

The `s3_file_trackings` table stores:

- `s3_bucket` - S3 bucket name
- `s3_key` - S3 object key (file path)
- `s3_url` - Complete S3 URL
- `fingerprint` - Unique content fingerprint (SHA256-based)
- `event_type` - CREATE, UPDATE, or DELETE
- `file_extension` - File extension
- `file_size` - File size in bytes
- `content_type` - MIME content type
- `etag` - S3 ETag
- `s3_last_modified` - S3 last modified timestamp
- `created_at` / `updated_at` - Record timestamps

## Event Trigger Configuration

This Lambda function is triggered by S3 bucket events through EventBridge. Configure your S3 bucket to send events:

### S3 Bucket Event Configuration

```json
{
  "Rules": [
    {
      "Name": "FileTrackingRule",
      "Status": "Enabled",
      "Filter": {
        "Prefix": ""
      },
      "LambdaConfigurations": [
        {
          "Id": "FileTracker",
          "LambdaFunctionArn": "arn:aws:lambda:region:account:function:xl4-s3-file-tracker",
          "Events": [
            "s3:ObjectCreated:*",
            "s3:ObjectRemoved:*"
          ]
        }
      ]
    }
  ]
}
```

### EventBridge Rule (Alternative)

You can also configure through EventBridge for more advanced filtering:

```json
{
  "Name": "S3FileTrackingRule",
  "EventPattern": {
    "source": ["aws.s3"],
    "detail-type": ["Object Created", "Object Deleted"],
    "detail": {
      "bucket": {
        "name": ["your-configured-bucket-name"]
      }
    }
  },
  "Targets": [
    {
      "Id": "S3FileTrackerTarget",
      "Arn": "arn:aws:lambda:region:account:function:xl4-s3-file-tracker"
    }
  ]
}
```

## Event Processing

This Lambda function processes S3 events automatically. It does **NOT** expose API endpoints - it's purely event-driven.

### Supported S3 Events

The function processes the following S3 events:

- **`s3:ObjectCreated:*`** - File creation/upload events
  - `s3:ObjectCreated:Put`
  - `s3:ObjectCreated:Post`
  - `s3:ObjectCreated:Copy`
  - `s3:ObjectCreated:CompleteMultipartUpload`

- **`s3:ObjectRemoved:*`** - File deletion events
  - `s3:ObjectRemoved:Delete`
  - `s3:ObjectRemoved:DeleteMarkerCreated`

### Event Processing Flow

1. **S3 Event Triggered** → File operation occurs in configured bucket
2. **Lambda Invoked** → EventBridge/S3 triggers this Lambda function
3. **Event Parsed** → Extract bucket, key, and event type from S3 event
4. **Fingerprint Generated** → Calculate file content fingerprint (for create/update)
5. **Database Updated** → Store/update file metadata in `s3_file_trackings` table

### Sample S3 Event Structure

```json
{
  "Records": [
    {
      "eventVersion": "2.1",
      "eventSource": "aws:s3",
      "eventName": "ObjectCreated:Put",
      "s3": {
        "bucket": {
          "name": "your-bucket-name"
        },
        "object": {
          "key": "path/to/file.apk",
          "size": 1024000
        }
      }
    }
  ]
}
```

## Querying Tracked Files

While this Lambda doesn't expose APIs, you can query the tracked files through the main API server at `/v1/s3/files` endpoints (handled by `xl4_delta_server`).

## Fingerprint Generation

The system uses intelligent fingerprinting:

- **Small files (≤50MB)**: Full content SHA256 hash
- **Large files (>50MB)**: Combined hash of metadata + first/last 1KB
- **Fallback**: Metadata-based hash if file access fails

## Environment Variables

Required environment variables:

- `DB_ADAPTER` - Database adapter (postgresql)
- `DB_HOST` - Database host
- `DB_PORT` - Database port
- `DB_USER_NAME` - Database username
- `DB_PASSWORD` - Database password
- `DB_NAME` - Database name
- `AWS_DEFAULT_REGION` - AWS region

## Deployment

1. Build and deploy the Lambda function
2. Configure S3 bucket event notifications
3. Set up appropriate IAM permissions
4. Run database migrations

## Use Cases

- **Duplicate File Detection**: Find identical files across buckets
- **File Audit Trail**: Track all file operations with timestamps
- **Content-Based Search**: Find files by fingerprint regardless of location
- **Storage Optimization**: Identify duplicate files for cleanup
- **Compliance**: Maintain complete file operation history
- **Integration**: Use fingerprints for delta processing and binary management
