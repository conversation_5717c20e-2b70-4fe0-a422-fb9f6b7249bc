# S3 File Tracker Lambda

This Lambda function automatically tracks all file operations (CRUD) in configured S3 buckets and stores metadata including file fingerprints in the database.

## Features

- **Automatic File Tracking**: Responds to S3 bucket events (Create, Update, Delete)
- **File Fingerprinting**: Generates unique fingerprints for file content identification
- **Metadata Storage**: Stores comprehensive file information in database
- **Duplicate Detection**: Identifies duplicate files across buckets
- **File History**: Tracks complete history of file operations
- **Search & Query**: Powerful search capabilities for tracked files

## Database Schema

The `s3_file_trackings` table stores:

- `s3_bucket` - S3 bucket name
- `s3_key` - S3 object key (file path)
- `s3_url` - Complete S3 URL
- `fingerprint` - Unique content fingerprint (SHA256-based)
- `event_type` - CREATE, UPDATE, or DELETE
- `file_extension` - File extension
- `file_size` - File size in bytes
- `content_type` - MIME content type
- `etag` - S3 ETag
- `s3_last_modified` - S3 last modified timestamp
- `created_at` / `updated_at` - Record timestamps

## S3 Event Configuration

Configure your S3 bucket to send events to this Lambda function:

```json
{
  "Rules": [
    {
      "Name": "FileTrackingRule",
      "Status": "Enabled",
      "Filter": {
        "Prefix": ""
      },
      "CloudWatchConfiguration": {
        "CloudWatchConfiguration": {
          "LogGroupName": "/aws/lambda/s3-file-tracker"
        }
      },
      "LambdaConfigurations": [
        {
          "Id": "FileTracker",
          "LambdaFunctionArn": "arn:aws:lambda:region:account:function:s3-file-tracker",
          "Events": [
            "s3:ObjectCreated:*",
            "s3:ObjectRemoved:*"
          ]
        }
      ]
    }
  ]
}
```

## API Endpoints

The system provides REST API endpoints through `/v1/s3/files`:

### Search Files
```json
POST /v1/s3/files
{
  "action": "search",
  "params": {
    "bucket": "my-bucket",
    "event_type": "CREATE",
    "file_extension": ".apk",
    "min_size": 1000000,
    "key_pattern": "android",
    "limit": 50
  }
}
```

### Find by Fingerprint
```json
POST /v1/s3/files
{
  "action": "find_by_fingerprint",
  "fingerprint": "abc123def456..."
}
```

### Bucket Statistics
```json
POST /v1/s3/files
{
  "action": "bucket_stats",
  "bucket": "my-bucket"
}
```

### Find Duplicates
```json
POST /v1/s3/files
{
  "action": "find_duplicates",
  "bucket": "my-bucket"  // optional
}
```

### File History
```json
POST /v1/s3/files
{
  "action": "file_history",
  "bucket": "my-bucket",
  "key": "path/to/file.apk"
}
```

### Cleanup Old Records
```json
POST /v1/s3/files
{
  "action": "cleanup",
  "days_old": 30
}
```

## Fingerprint Generation

The system uses intelligent fingerprinting:

- **Small files (≤50MB)**: Full content SHA256 hash
- **Large files (>50MB)**: Combined hash of metadata + first/last 1KB
- **Fallback**: Metadata-based hash if file access fails

## Environment Variables

Required environment variables:

- `DB_ADAPTER` - Database adapter (postgresql)
- `DB_HOST` - Database host
- `DB_PORT` - Database port
- `DB_USER_NAME` - Database username
- `DB_PASSWORD` - Database password
- `DB_NAME` - Database name
- `AWS_DEFAULT_REGION` - AWS region

## Deployment

1. Build and deploy the Lambda function
2. Configure S3 bucket event notifications
3. Set up appropriate IAM permissions
4. Run database migrations

## Use Cases

- **Duplicate File Detection**: Find identical files across buckets
- **File Audit Trail**: Track all file operations with timestamps
- **Content-Based Search**: Find files by fingerprint regardless of location
- **Storage Optimization**: Identify duplicate files for cleanup
- **Compliance**: Maintain complete file operation history
- **Integration**: Use fingerprints for delta processing and binary management
