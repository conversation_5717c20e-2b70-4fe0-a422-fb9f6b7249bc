require 'json'
require 'active_record'
require 'aws-sdk-s3'
require 'digest'
require 'uri'
require 'tempfile'

# Load models and services
require_relative '../xl4_delta_server/models/s3_file_tracking'
require_relative '../xl4_delta_server/services/fingerprint_service'
require_relative '../xl4_delta_server/services/s3_event_handler'

# Database connection
ActiveRecord::Base.establish_connection(
  adapter: ENV['DB_ADAPTER'],
  host: <PERSON><PERSON><PERSON>['DB_HOST'],
  port: EN<PERSON>['DB_PORT'],
  username: <PERSON><PERSON><PERSON>['DB_USER_NAME'],
  password: ENV['DB_PASSWORD'],
  database: ENV['DB_NAME']
)

def lambda_handler(event:, context:)
  puts "S3 File Tracker Lambda triggered"
  puts "Event: #{event.to_json}"
  puts "Context: #{context.to_json}"
  
  response = {
    statusCode: 200,
    body: {
      message: "S3 file tracking completed",
      processed_records: 0,
      errors: []
    }
  }
  
  begin
    # Process S3 event records
    records = event['Records'] || []
    puts "Processing #{records.length} S3 event records"
    
    records.each_with_index do |record, index|
      begin
        puts "Processing record #{index + 1}/#{records.length}"
        
        # Check if this is an S3 event
        if record['eventSource'] == 'aws:s3'
          S3EventHandler.process_s3_event(record)
          response[:body][:processed_records] += 1
        else
          puts "Skipping non-S3 event: #{record['eventSource']}"
        end
        
      rescue => e
        error_msg = "Error processing record #{index + 1}: #{e.message}"
        puts error_msg
        puts e.backtrace.join("\n")
        response[:body][:errors] << error_msg
      end
    end
    
    puts "Successfully processed #{response[:body][:processed_records]} S3 events"
    
  rescue => e
    puts "Error in S3 file tracker lambda: #{e.message}"
    puts e.backtrace.join("\n")
    
    response[:statusCode] = 500
    response[:body][:message] = "Error processing S3 events"
    response[:body][:errors] << e.message
  end
  
  # Return response
  response[:body] = response[:body].to_json
  puts "Lambda response: #{response.to_json}"
  
  response
end
