# Database Setup and Migrations

This document explains how to set up and run the database migrations for the XL4 Delta Server SAM application.

## Database Schema Overview

The application uses PostgreSQL with the following main tables:

### Core Tables

1. **`ecs_jobs`** - Tracks ECS task executions
2. **`ecs_job_results`** - Stores ECS task execution results
3. **`ecs_job_metadatas`** - Stores metadata for delta processing jobs
4. **`s3_file_trackings`** - Tracks S3 file operations and fingerprints

## Prerequisites

- PostgreSQL 12+ installed and running
- Ruby 3.1.3+
- Rails 7.2+
- Database credentials configured in environment variables

## Environment Variables

Set the following environment variables before running migrations:

```bash
export DB_ADAPTER=postgresql
export DB_HOST=localhost
export DB_PORT=5432
export DB_USER_NAME=your_username
export DB_PASSWORD=your_password
export DB_NAME=xl4_delta_server_development
```

## Database Setup

### 1. Create Database

First, create the database:

```bash
# Using psql
createdb xl4_delta_server_development

# Or using SQL
psql -c "CREATE DATABASE xl4_delta_server_development;"
```

### 2. Install Dependencies

```bash
# Install Ruby gems
bundle install
```

### 3. Run Migrations

Run all migrations in order:

```bash
# Run all pending migrations
rails db:migrate

# Or run them individually in order:
rails db:migrate:up VERSION=20241001124500  # Create ecs_jobs
rails db:migrate:up VERSION=20241001124501  # Create ecs_job_results
rails db:migrate:up VERSION=20241001124502  # Create ecs_job_metadata
rails db:migrate:up VERSION=20241001124503  # Create s3_file_tracking
```

### 4. Verify Migration Status

```bash
# Check migration status
rails db:migrate:status
```

Expected output:
```
Status   Migration ID    Migration Name
--------------------------------------------------
   up    20241001124500  Create ecs jobs
   up    20241001124501  Create ecs job results
   up    20241001124502  Create ecs job metadata
   up    20241001124503  Create s3 file tracking
```

## Migration Details

### Migration 1: ECS Jobs (20241001124500)
Creates the main `ecs_jobs` table to track ECS task executions.

**Table: `ecs_jobs`**
- `id` (Primary Key)
- `task` (String) - ECS task ID
- `created_at`, `updated_at` (Timestamps)

**Indexes:**
- `index_ecs_jobs_on_task`
- `index_ecs_jobs_on_created_at`

### Migration 2: ECS Job Results (20241001124501)
Creates the `ecs_job_results` table to store task execution results.

**Table: `ecs_job_results`**
- `id` (Primary Key)
- `state` (String) - Task state (PENDING, RUNNING, STOPPED)
- `message` (String) - Error or status message
- `exit_code` (Integer) - Task exit code
- `ecs_job_id` (Foreign Key) - References ecs_jobs(id)
- `created_at`, `updated_at` (Timestamps)

**Indexes:**
- `index_ecs_job_results_on_state`
- `index_ecs_job_results_on_exit_code`
- `index_ecs_job_results_on_created_at`

### Migration 3: ECS Job Metadata (20241001124502)
Creates the `ecs_job_metadatas` table for delta processing metadata.

**Table: `ecs_job_metadatas`**
- `id` (Primary Key)
- `compatibility` (String) - Compatibility string
- `src_path` (String) - Source file S3 path
- `src_size` (BigInt) - Source file size in bytes
- `dest_path` (String) - Destination file S3 path
- `dest_size` (BigInt) - Destination file size in bytes
- `delta_path` (String) - Delta file S3 path
- `delta_size` (BigInt) - Delta file size in bytes
- `delta_percentage` (Integer) - Delta compression percentage
- `ecs_job_id` (Foreign Key) - References ecs_jobs(id)
- `created_at`, `updated_at` (Timestamps)

**Indexes:**
- `index_ecs_job_metadatas_on_compatibility`
- `index_ecs_job_metadatas_on_src_path`
- `index_ecs_job_metadatas_on_dest_path`
- `index_ecs_job_metadatas_on_delta_path`
- `index_ecs_job_metadatas_on_created_at`

### Migration 4: S3 File Tracking (20241001124503)
Creates the `s3_file_trackings` table for S3 file operation tracking.

**Table: `s3_file_trackings`**
- `id` (Primary Key)
- `s3_bucket` (String, NOT NULL) - S3 bucket name
- `s3_key` (String, NOT NULL) - S3 object key
- `s3_url` (String, NOT NULL) - Complete S3 URL
- `fingerprint` (String) - File content fingerprint
- `event_type` (String, NOT NULL) - CREATE, UPDATE, DELETE
- `file_extension` (String) - File extension
- `file_size` (BigInt) - File size in bytes
- `content_type` (String) - MIME content type
- `etag` (String) - S3 ETag
- `s3_last_modified` (DateTime) - S3 last modified timestamp
- `created_at`, `updated_at` (Timestamps)

**Indexes:**
- `index_s3_file_trackings_on_bucket_key` (Composite: s3_bucket, s3_key)
- `index_s3_file_trackings_on_fingerprint`
- `index_s3_file_trackings_on_event_type`
- `index_s3_file_trackings_on_created_at`

## Rollback Migrations

If you need to rollback migrations:

```bash
# Rollback last migration
rails db:rollback

# Rollback specific migration
rails db:migrate:down VERSION=20241001124503

# Rollback multiple steps
rails db:rollback STEP=2
```

## Database Console

Access the database console:

```bash
# Rails console with database access
rails console

# Direct PostgreSQL access
psql xl4_delta_server_development
```

## Troubleshooting

### Common Issues

1. **Connection refused**
   - Ensure PostgreSQL is running
   - Check connection parameters

2. **Permission denied**
   - Verify database user has CREATE privileges
   - Check database ownership

3. **Migration already exists**
   - Check migration status: `rails db:migrate:status`
   - Use specific version numbers if needed

### Reset Database

To completely reset the database:

```bash
# Drop and recreate database
rails db:drop db:create db:migrate

# Or manually
dropdb xl4_delta_server_development
createdb xl4_delta_server_development
rails db:migrate
```

## Production Considerations

For production deployments:

1. Use environment-specific database names
2. Set up proper database backups
3. Monitor migration performance
4. Consider zero-downtime migration strategies
5. Set up database monitoring and alerting
