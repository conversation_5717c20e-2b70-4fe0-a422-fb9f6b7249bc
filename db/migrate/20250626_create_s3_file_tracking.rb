class CreateS3FileTracking < ActiveRecord::Migration[7.2]
  def change
    create_table :s3_file_trackings do |t|
      t.string :s3_bucket, null: false
      t.string :s3_key, null: false
      t.string :s3_url, null: false
      t.string :fingerprint
      t.string :event_type, null: false # CREATE, UPDATE, DELETE
      t.string :file_extension
      t.bigint :file_size
      t.string :content_type
      t.string :etag
      t.datetime :s3_last_modified
      t.timestamps

      t.index [:s3_bucket, :s3_key], name: 'index_s3_file_trackings_on_bucket_key'
      t.index :fingerprint, name: 'index_s3_file_trackings_on_fingerprint'
      t.index :event_type, name: 'index_s3_file_trackings_on_event_type'
      t.index :created_at, name: 'index_s3_file_trackings_on_created_at'
    end
  end
end
