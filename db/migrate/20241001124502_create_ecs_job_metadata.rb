class CreateEcsJobMetadata < ActiveRecord::Migration[7.2]
  def change
    create_table :ecs_job_metadatas do |t|
      t.string :compatibility
      t.string :src_path
      t.bigint :src_size
      t.string :dest_path
      t.bigint :dest_size
      t.string :delta_path
      t.bigint :delta_size
      t.integer :delta_percentage
      t.references :ecs_job, null: false, foreign_key: true
      t.timestamps
    end

    add_index :ecs_job_metadatas, :compatibility, name: 'index_ecs_job_metadatas_on_compatibility'
    add_index :ecs_job_metadatas, :src_path, name: 'index_ecs_job_metadatas_on_src_path'
    add_index :ecs_job_metadatas, :dest_path, name: 'index_ecs_job_metadatas_on_dest_path'
    add_index :ecs_job_metadatas, :delta_path, name: 'index_ecs_job_metadatas_on_delta_path'
    add_index :ecs_job_metadatas, :created_at, name: 'index_ecs_job_metadatas_on_created_at'
  end
end
