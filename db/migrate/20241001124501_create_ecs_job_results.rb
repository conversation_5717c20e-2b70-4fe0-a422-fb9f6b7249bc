class CreateEcsJobResults < ActiveRecord::Migration[7.2]
  def change
    create_table :ecs_job_results do |t|
      t.string :state
      t.string :message
      t.integer :exit_code
      t.references :ecs_job, null: false, foreign_key: true
      t.timestamps
    end

    add_index :ecs_job_results, :state, name: 'index_ecs_job_results_on_state'
    add_index :ecs_job_results, :exit_code, name: 'index_ecs_job_results_on_exit_code'
    add_index :ecs_job_results, :created_at, name: 'index_ecs_job_results_on_created_at'
  end
end
