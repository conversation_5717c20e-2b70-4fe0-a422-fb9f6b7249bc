require 'json'
require 'active_record'
require 'time'
require_relative 'models/ecs_job.rb'
require_relative 'models/ecs_job_result.rb'
require_relative 'models/ecs_job_metadata.rb'
require 'aws-sdk-s3'

ActiveRecord::Base.establish_connection(
  adapter: ENV['DB_ADAPTER'],
  host: EN<PERSON>['DB_HOST'],
  port: <PERSON>N<PERSON>['DB_PORT'],
  username: <PERSON><PERSON><PERSON>['DB_USER_NAME'],
  password: EN<PERSON>['DB_PASSWORD'],
  database: ENV['DB_NAME']
)

def get_s3_size(region,s3_path )
  s3_client = Aws::S3::Client.new(region: region)
  uri = URI.parse(s3_path)
  bucket_name = uri.host
  object_key = uri.path[1..-1]
  response = s3_client.head_object(bucket: bucket_name, key: object_key)
  response.content_length
end

def lambda_handler(event:, context:)
  puts "Event: #{event.to_json}"
  puts "Context: #{context.to_json}"

  response = {
    "containerState" => "Unknown",
    "exitCode" => nil,
    "failureReason" => nil,
    "taskArn" => "Unknown",
    "taskId" => "Unknown",
    "updatedAt" => "Unknown"
  }
  begin
    container_info = event.dig("detail", "containers")&.first || {}
    response["containerState"] = container_info["lastStatus"] || "Unknown"
    response["exitCode"] = container_info["exitCode"]
    response["failureReason"] = event.dig("detail", "stoppedReason") || "No failure reason"
    response["taskArn"] = event.dig("detail", "taskArn") || "Unknown"
    if response["taskArn"] != "Unknown"
      response["taskId"] = response["taskArn"].split('/').last
    else
      response["taskId"] = "Unknown"
    end
    response["updatedAt"] = event.dig("detail", "updatedAt") || "Unknown" 
  rescue IndexError
    response["failureReason"] = "No containers found in event details."
  end
  ecs_job = EcsJob.find_by(task: response["taskId"])
  ecs_job.ecs_job_results.create(state: response["containerState"], message: response["failureReason"], exit_code: response["exitCode"])

  if response["exitCode"] == 0
    ecs_job_metadata = ecs_job.ecs_job_metadatas.last
    s3_client = Aws::S3::Client.new(region: ENV['AWS_DEFAULT_REGION'])
    uri = URI.parse(ecs_job_metadata.delta_path)
    bucket_name = uri.host
    object_key = uri.path[1..-1]
    response = s3_client.head_object(bucket: bucket_name, key: object_key)
    puts response.content_length
    ecs_job_metadata.delta_size = response.content_length
    percentage = (100 - (ecs_job_metadata.delta_size.to_f / ecs_job_metadata.dest_size.to_f) * 100)
    ecs_job_metadata.delta_percentage = percentage
    ecs_job_metadata.save
  end

  puts response.to_json
  {
    statusCode: 200,
    body: response.to_json
  }
end