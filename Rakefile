require 'rake'
require 'active_record'
require 'yaml'
require 'logger'

# Load database configuration from environment variables or YAML
def load_database_config
  if ENV['DB_ADAPTER']
    {
      adapter: ENV['DB_ADAPTER'],
      host: ENV['DB_HOST'] || 'localhost',
      port: <PERSON>N<PERSON>['DB_PORT'] || 5432,
      username: <PERSON><PERSON><PERSON>['DB_USER_NAME'],
      password: ENV['DB_PASSWORD'],
      database: ENV['DB_NAME']
    }
  else
    YAML.load_file('config/database.yml')
  end
end

db_config = load_database_config
ActiveRecord::Base.establish_connection(db_config)
ActiveRecord::Base.logger = Logger.new(STDOUT)

namespace :db do
  desc 'Create the database'
  task :create do
    config = load_database_config
    database_name = config[:database]
    config_without_db = config.dup
    config_without_db.delete(:database)

    ActiveRecord::Base.establish_connection(config_without_db)
    ActiveRecord::Base.connection.create_database(database_name)
    puts "Database '#{database_name}' created successfully"
  rescue ActiveRecord::DatabaseAlreadyExists
    puts "Database '#{database_name}' already exists"
  end

  desc 'Drop the database'
  task :drop do
    config = load_database_config
    database_name = config[:database]
    config_without_db = config.dup
    config_without_db.delete(:database)

    ActiveRecord::Base.establish_connection(config_without_db)
    ActiveRecord::Base.connection.drop_database(database_name)
    puts "Database '#{database_name}' dropped successfully"
  rescue ActiveRecord::NoDatabaseError
    puts "Database '#{database_name}' does not exist"
  end

  desc 'Migrate the database (run all migrations)'
  task :migrate do
    config = load_database_config
    ActiveRecord::Base.establish_connection(config)

    ActiveRecord::MigrationContext.new("db/migrate", ActiveRecord::SchemaMigration).migrate
    puts "Migrations completed successfully"
  end

  desc 'Roll back the last migration'
  task :rollback do
    config = load_database_config
    ActiveRecord::Base.establish_connection(config)

    ActiveRecord::MigrationContext.new("db/migrate", ActiveRecord::SchemaMigration).rollback
    puts "Rollback completed successfully"
  end

  desc 'Show migration status'
  task :status do
    config = load_database_config
    ActiveRecord::Base.establish_connection(config)

    context = ActiveRecord::MigrationContext.new("db/migrate", ActiveRecord::SchemaMigration)
    puts "Status   Migration ID    Migration Name"
    puts "--------------------------------------------------"

    context.migrations.each do |migration|
      status = context.get_all_versions.include?(migration.version) ? "up" : "down"
      puts "#{status.ljust(8)} #{migration.version}  #{migration.name}"
    end
  end

  desc 'Reset database (drop, create, migrate)'
  task :reset => [:drop, :create, :migrate] do
    puts "Database reset completed successfully"
  end

  desc 'Setup database (create and migrate)'
  task :setup => [:create, :migrate] do
    puts "Database setup completed successfully"
  end

  desc 'Dump the schema'
  task :schema_dump do
    ActiveRecord::SchemaDumper.dump(ActiveRecord::Base.connection, File.open('db/schema.rb', 'w'))
    puts 'Schema dumped to db/schema.rb'
  end

  desc 'Load the schema'
  task :schema_load do
    load('db/schema.rb')
  end
end