# Android Fingerprint Extraction

The XL4 Delta Server automatically extracts Android build fingerprints from base and target files during the `execute2` operation. This feature reads the `SYSTEM/build.prop` file from Android APK/system images and extracts the `ro.system.build.fingerprint` value.

## Overview

When a delta operation is initiated via `/v1/delta/execute2`, the system:

1. **Downloads** base and target files from S3
2. **Extracts** Android fingerprints from both files
3. **Stores** fingerprints in the database
4. **Proceeds** with the delta operation
5. **Returns** fingerprint information in the summary

## Supported File Types

### APK Files (`.apk`)
- Searches for `SYSTEM/build.prop` inside the APK
- Falls back to alternative locations:
  - `system/build.prop`
  - `META-INF/com/google/android/updater-script`
  - `system/etc/build.prop`

### ZIP Files (`.zip`)
- Same logic as APK files
- Useful for system image archives

### IMG Files (`.img`)
- Uses `strings` command to extract fingerprint
- Searches for `ro.system.build.fingerprint` in the image

## Fingerprint Format

The extracted fingerprint follows Android's standard format:

```
Android/aosp_arm64/generic_arm64:VanillaIceCream/AP3A.240905.015.A2/eng.root.00000000.000000:eng/test-keys
```

This corresponds to the shell command:
```bash
cat SYSTEM/build.prop | grep ro.system.build.fingerprint | cut -d'=' -f2
```

## Database Schema

The fingerprints are stored in the `ecs_job_metadatas` table:

```sql
ALTER TABLE ecs_job_metadatas 
ADD COLUMN base_android_fingerprint VARCHAR,
ADD COLUMN target_android_fingerprint VARCHAR;
```

## API Integration

### Execute2 Request
```json
POST /v1/delta/execute2
{
  "capabilities": "A:3;B:3;C:100;E:2.6",
  "base": {
    "_o": "URLBinaryReference",
    "url": "s3://bucket/base-system.apk"
  },
  "target": {
    "_o": "URLBinaryReference", 
    "url": "s3://bucket/target-system.apk"
  },
  "store": {
    "_o": "S3ObjectStorageLocation",
    "s3Url": "s3://bucket/delta-output"
  }
}
```

### Summary Response
```json
POST /v1/delta/summary
{
  "token": "task-id"
}

Response:
{
  "compatibility": "A:3;B:3;C:100;E:2.6",
  "src_path": "s3://bucket/base-system.apk",
  "dest_path": "s3://bucket/target-system.apk",
  "base_android_fingerprint": "Android/aosp_arm64/generic_arm64:VanillaIceCream/AP3A.240905.015.A2/eng.root.00000000.000000:eng/test-keys",
  "target_android_fingerprint": "Android/aosp_arm64/generic_arm64:VanillaIceCream/AP3A.240905.015.A3/eng.root.00000000.000000:eng/test-keys",
  "android_fingerprints_match": false,
  "status": "COMPLETED",
  "delta_size": 1024000,
  "delta_percentage": 15
}
```

## Implementation Details

### AndroidFingerprintService

The `AndroidFingerprintService` handles fingerprint extraction:

```ruby
# Extract from both files
result = AndroidFingerprintService.extract_fingerprints_from_both_files(base_url, target_url)

# Result structure
{
  base_fingerprint: "Android/...",
  target_fingerprint: "Android/...",
  fingerprints_match: false
}
```

### Error Handling

- **File not found**: Returns `nil` for fingerprint
- **Invalid format**: Logs error and returns `nil`
- **Unsupported file type**: Logs warning and returns `nil`
- **Network errors**: Retries download with exponential backoff

### Performance Considerations

- **Temporary files**: Automatically cleaned up after processing
- **Memory usage**: Streams large files to avoid memory issues
- **Parallel processing**: Base and target files processed concurrently
- **Caching**: Fingerprints cached in database for future reference

## Database Queries

### Find jobs with matching fingerprints
```ruby
EcsJobMetadata.with_matching_fingerprints
```

### Find jobs with specific base fingerprint
```ruby
EcsJobMetadata.with_base_fingerprint("Android/aosp_arm64/...")
```

### Find jobs with different fingerprints
```ruby
EcsJobMetadata.with_different_fingerprints
```

## Monitoring and Logging

The service provides detailed logging:

```
Extracting Android fingerprints from base and target files...
Downloading s3://bucket/base-system.apk
Downloaded to temporary file: /tmp/android_file20241001-123.apk (50MB)
Extracting fingerprint from APK: /tmp/android_file20241001-123.apk
Found SYSTEM/build.prop in APK
Found fingerprint: Android/aosp_arm64/generic_arm64:VanillaIceCream/...
Fingerprint extraction results:
  Base: Android/aosp_arm64/generic_arm64:VanillaIceCream/AP3A.240905.015.A2/...
  Target: Android/aosp_arm64/generic_arm64:VanillaIceCream/AP3A.240905.015.A3/...
  Match: false
```

## Testing

Run the test script to verify functionality:

```bash
cd xl4_delta_server
ruby test_android_fingerprint.rb
```

## Use Cases

1. **Version Tracking**: Track Android version changes between base and target
2. **Compatibility Checking**: Ensure delta operations are between compatible builds
3. **Audit Trail**: Maintain history of Android build fingerprints
4. **Quality Assurance**: Verify correct Android builds are being processed
5. **Analytics**: Analyze patterns in Android build updates

## Troubleshooting

### Common Issues

1. **Fingerprint not found**
   - Check if file contains `SYSTEM/build.prop`
   - Verify file is a valid Android APK/system image
   - Check alternative locations in the archive

2. **Download failures**
   - Verify S3 permissions
   - Check network connectivity
   - Validate S3 URL format

3. **Memory issues with large files**
   - Monitor Lambda memory usage
   - Consider increasing Lambda memory allocation
   - Check temporary disk space

### Debug Commands

```bash
# Test fingerprint extraction locally
ruby -r './services/android_fingerprint_service' -e "puts AndroidFingerprintService.extract_fingerprint_from_s3_file('s3://bucket/file.apk')"

# Check database records
psql -c "SELECT base_android_fingerprint, target_android_fingerprint FROM ecs_job_metadatas WHERE base_android_fingerprint IS NOT NULL;"
```
