# Database Configuration
DB_ADAPTER=postgresql
DB_HOST=localhost
DB_PORT=5432
DB_USER_NAME=your_username
DB_PASSWORD=your_password
DB_NAME=xl4_delta_server_development

# AWS Configuration
AWS_DEFAULT_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key

# ECS Configuration
ECS_CLUSTER_NAME=your-ecs-cluster
ECS_TASK_DEFINITION_SCALE2=your-task-definition
ECS_SUBNET_IDS=subnet-12345,subnet-67890
ECS_SECURITY_GROUP_IDS=sg-12345,sg-67890

# S3 Configuration
S3_BUCKET=your-default-bucket
DOWNLOAD_S3_BUCKET=your-download-bucket
S3_TRACKING_BUCKET=your-tracking-bucket

# Application Configuration
RAILS_ENV=development
