require_relative 'fingerprint_service'
require_relative '../models/s3_file_tracking'

class S3EventHandler
  def self.process_s3_event(event_record)
    puts "Processing S3 event: #{event_record.inspect}"
    
    # Extract S3 event information
    s3_info = event_record.dig('s3')
    return unless s3_info
    
    bucket_name = s3_info.dig('bucket', 'name')
    object_key = s3_info.dig('object', 'key')
    event_name = event_record['eventName']
    
    return unless bucket_name && object_key
    
    # URL decode the object key (S3 events URL encode keys)
    object_key = URI.decode_www_form_component(object_key)
    
    puts "Processing S3 event - Bucket: #{bucket_name}, Key: #{object_key}, Event: #{event_name}"
    
    # Determine event type
    event_type = map_event_type(event_name)
    
    case event_type
    when 'CREATE', 'UPDATE'
      handle_create_or_update_event(bucket_name, object_key, event_type)
    when 'DELETE'
      handle_delete_event(bucket_name, object_key)
    else
      puts "Unhandled event type: #{event_name}"
    end
  end

  private

  def self.map_event_type(event_name)
    case event_name
    when /ObjectCreated:Put/, /ObjectCreated:Post/, /ObjectCreated:Copy/, /ObjectCreated:CompleteMultipartUpload/
      'CREATE'
    when /ObjectRemoved:Delete/, /ObjectRemoved:DeleteMarkerCreated/
      'DELETE'
    else
      # For any other events that modify the object, treat as UPDATE
      'UPDATE'
    end
  end

  def self.handle_create_or_update_event(bucket_name, object_key, event_type)
    begin
      # Generate fingerprint and extract file info
      fingerprint = FingerprintService.generate_fingerprint(bucket_name, object_key)
      file_info = FingerprintService.extract_file_info(bucket_name, object_key)
      
      # Create S3 URL
      s3_url = "s3://#{bucket_name}/#{object_key}"
      
      # Check if we already have a record for this file
      existing_record = S3FileTracking.find_by_s3_location(bucket_name, object_key)
      
      if existing_record
        # Update existing record
        existing_record.update!(
          fingerprint: fingerprint,
          event_type: event_type,
          file_size: file_info[:file_size],
          content_type: file_info[:content_type],
          etag: file_info[:etag],
          s3_last_modified: file_info[:last_modified],
          file_extension: file_info[:file_extension]
        )
        puts "Updated existing S3 file tracking record for #{s3_url}"
      else
        # Create new record
        S3FileTracking.create!(
          s3_bucket: bucket_name,
          s3_key: object_key,
          s3_url: s3_url,
          fingerprint: fingerprint,
          event_type: event_type,
          file_size: file_info[:file_size],
          content_type: file_info[:content_type],
          etag: file_info[:etag],
          s3_last_modified: file_info[:last_modified],
          file_extension: file_info[:file_extension]
        )
        puts "Created new S3 file tracking record for #{s3_url}"
      end
      
    rescue => e
      puts "Error handling S3 create/update event: #{e.message}"
      puts e.backtrace.join("\n")
    end
  end

  def self.handle_delete_event(bucket_name, object_key)
    begin
      s3_url = "s3://#{bucket_name}/#{object_key}"
      
      # Find existing record
      existing_record = S3FileTracking.find_by_s3_location(bucket_name, object_key)
      
      if existing_record
        # Update the record to mark as deleted
        existing_record.update!(
          event_type: 'DELETE'
        )
        puts "Marked S3 file tracking record as deleted for #{s3_url}"
      else
        # Create a delete record even if we don't have the original
        S3FileTracking.create!(
          s3_bucket: bucket_name,
          s3_key: object_key,
          s3_url: s3_url,
          fingerprint: nil,
          event_type: 'DELETE',
          file_size: nil,
          content_type: nil,
          etag: nil,
          s3_last_modified: nil,
          file_extension: File.extname(object_key).downcase
        )
        puts "Created delete record for #{s3_url}"
      end
      
    rescue => e
      puts "Error handling S3 delete event: #{e.message}"
      puts e.backtrace.join("\n")
    end
  end
end
