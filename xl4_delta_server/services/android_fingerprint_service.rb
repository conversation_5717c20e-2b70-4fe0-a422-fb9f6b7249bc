require 'aws-sdk-s3'
require 'tempfile'
require 'zip'
require 'open3'

class AndroidFingerprintService
  def self.extract_fingerprint_from_s3_file(s3_url)
    puts "Extracting Android fingerprint from: #{s3_url}"
    
    begin
      # Parse S3 URL
      uri = URI.parse(s3_url)
      bucket_name = uri.host
      object_key = uri.path[1..-1] # Remove leading slash
      
      # Download file to temporary location
      temp_file = download_s3_file(bucket_name, object_key)
      
      # Extract fingerprint based on file type
      fingerprint = extract_fingerprint_from_file(temp_file.path)
      
      puts "Extracted fingerprint: #{fingerprint}"
      fingerprint
      
    rescue => e
      puts "Error extracting fingerprint from #{s3_url}: #{e.message}"
      puts e.backtrace.join("\n")
      nil
    ensure
      temp_file&.unlink if temp_file
    end
  end

  private

  def self.download_s3_file(bucket, key)
    puts "Downloading s3://#{bucket}/#{key}"
    
    s3_client = Aws::S3::Client.new(region: ENV['AWS_DEFAULT_REGION'])
    temp_file = Tempfile.new(['android_file', File.extname(key)])
    temp_file.binmode
    
    s3_client.get_object(bucket: bucket, key: key) do |chunk|
      temp_file.write(chunk)
    end
    
    temp_file.close
    puts "Downloaded to temporary file: #{temp_file.path} (#{File.size(temp_file.path)} bytes)"
    
    temp_file
  end

  def self.extract_fingerprint_from_file(file_path)
    file_extension = File.extname(file_path).downcase
    
    case file_extension
    when '.apk'
      extract_fingerprint_from_apk(file_path)
    when '.zip'
      extract_fingerprint_from_zip(file_path)
    when '.img'
      extract_fingerprint_from_img(file_path)
    else
      puts "Unsupported file type: #{file_extension}"
      nil
    end
  end

  def self.extract_fingerprint_from_apk(apk_path)
    puts "Extracting fingerprint from APK: #{apk_path}"
    
    begin
      Zip::File.open(apk_path) do |zip_file|
        # Look for SYSTEM/build.prop in the APK
        build_prop_entry = zip_file.find_entry('SYSTEM/build.prop')
        
        if build_prop_entry
          puts "Found SYSTEM/build.prop in APK"
          build_prop_content = build_prop_entry.get_input_stream.read
          return extract_fingerprint_from_build_prop(build_prop_content)
        else
          puts "SYSTEM/build.prop not found in APK, checking other locations..."
          
          # Try alternative locations
          alternative_paths = [
            'system/build.prop',
            'META-INF/com/google/android/updater-script',
            'system/etc/build.prop'
          ]
          
          alternative_paths.each do |path|
            entry = zip_file.find_entry(path)
            if entry
              puts "Found build.prop at: #{path}"
              content = entry.get_input_stream.read
              fingerprint = extract_fingerprint_from_build_prop(content)
              return fingerprint if fingerprint
            end
          end
          
          puts "No build.prop found in APK"
          nil
        end
      end
    rescue => e
      puts "Error reading APK file: #{e.message}"
      nil
    end
  end

  def self.extract_fingerprint_from_zip(zip_path)
    puts "Extracting fingerprint from ZIP: #{zip_path}"
    extract_fingerprint_from_apk(zip_path) # Same logic as APK
  end

  def self.extract_fingerprint_from_img(img_path)
    puts "Extracting fingerprint from IMG: #{img_path}"
    
    begin
      # Try to mount the image and extract build.prop
      # This requires additional tools and permissions, so we'll use a simpler approach
      
      # Try to extract using strings command to find build.prop content
      stdout, stderr, status = Open3.capture3("strings #{img_path} | grep -A 50 -B 50 'ro.system.build.fingerprint'")
      
      if status.success? && stdout.include?('ro.system.build.fingerprint')
        puts "Found fingerprint in IMG file using strings"
        return extract_fingerprint_from_build_prop(stdout)
      else
        puts "Could not extract fingerprint from IMG file"
        nil
      end
    rescue => e
      puts "Error processing IMG file: #{e.message}"
      nil
    end
  end

  def self.extract_fingerprint_from_build_prop(content)
    puts "Parsing build.prop content for fingerprint"
    
    # Look for ro.system.build.fingerprint line
    content.each_line do |line|
      line = line.strip
      
      if line.start_with?('ro.system.build.fingerprint=')
        fingerprint = line.split('=', 2)[1]
        puts "Found fingerprint: #{fingerprint}"
        return fingerprint
      end
    end
    
    puts "ro.system.build.fingerprint not found in build.prop content"
    nil
  end

  def self.extract_fingerprints_from_both_files(base_s3_url, target_s3_url)
    puts "Extracting fingerprints from both base and target files"
    
    base_fingerprint = extract_fingerprint_from_s3_file(base_s3_url)
    target_fingerprint = extract_fingerprint_from_s3_file(target_s3_url)
    
    result = {
      base_fingerprint: base_fingerprint,
      target_fingerprint: target_fingerprint,
      fingerprints_match: base_fingerprint && target_fingerprint && base_fingerprint == target_fingerprint
    }
    
    puts "Fingerprint extraction results:"
    puts "  Base: #{base_fingerprint || 'Not found'}"
    puts "  Target: #{target_fingerprint || 'Not found'}"
    puts "  Match: #{result[:fingerprints_match]}"
    
    result
  end
end
