require 'digest'
require 'aws-sdk-s3'
require 'tempfile'

class FingerprintService
  def self.generate_fingerprint(s3_bucket, s3_key, region = ENV['AWS_DEFAULT_REGION'])
    begin
      puts "Generating fingerprint for s3://#{s3_bucket}/#{s3_key}"
      
      s3_client = Aws::S3::Client.new(region: region)
      
      # For small files, download and hash the entire content
      # For large files, use a combination of metadata and partial content
      object_info = s3_client.head_object(bucket: s3_bucket, key: s3_key)
      file_size = object_info.content_length
      
      if file_size <= 50 * 1024 * 1024 # 50MB threshold
        fingerprint = generate_full_content_fingerprint(s3_client, s3_bucket, s3_key)
      else
        fingerprint = generate_metadata_fingerprint(s3_client, s3_bucket, s3_key, object_info)
      end
      
      puts "Generated fingerprint: #{fingerprint}"
      fingerprint
    rescue => e
      puts "Error generating fingerprint: #{e.message}"
      # Fallback to a basic fingerprint based on metadata
      generate_fallback_fingerprint(s3_bucket, s3_key)
    end
  end

  private

  def self.generate_full_content_fingerprint(s3_client, bucket, key)
    temp_file = Tempfile.new(['fingerprint', '.tmp'])
    temp_file.binmode
    
    begin
      s3_client.get_object(bucket: bucket, key: key) do |chunk|
        temp_file.write(chunk)
      end
      temp_file.close
      
      # Generate SHA256 hash of the file content
      Digest::SHA256.file(temp_file.path).hexdigest
    ensure
      temp_file.unlink if temp_file
    end
  end

  def self.generate_metadata_fingerprint(s3_client, bucket, key, object_info)
    # For large files, create a fingerprint based on:
    # 1. File size
    # 2. Last modified time
    # 3. ETag
    # 4. First and last 1KB of content
    
    metadata_string = "#{object_info.content_length}:#{object_info.last_modified}:#{object_info.etag}"
    
    # Get first 1KB
    first_chunk = s3_client.get_object(bucket: bucket, key: key, range: 'bytes=0-1023').body.read
    
    # Get last 1KB
    last_start = [object_info.content_length - 1024, 0].max
    last_chunk = s3_client.get_object(bucket: bucket, key: key, range: "bytes=#{last_start}-#{object_info.content_length - 1}").body.read
    
    # Combine metadata and content samples
    combined_data = metadata_string + first_chunk + last_chunk
    Digest::SHA256.hexdigest(combined_data)
  end

  def self.generate_fallback_fingerprint(bucket, key)
    # Simple fallback based on bucket, key, and timestamp
    fallback_data = "#{bucket}:#{key}:#{Time.now.to_i}"
    Digest::SHA256.hexdigest(fallback_data)
  end

  def self.extract_file_info(s3_bucket, s3_key, region = ENV['AWS_DEFAULT_REGION'])
    begin
      s3_client = Aws::S3::Client.new(region: region)
      object_info = s3_client.head_object(bucket: s3_bucket, key: s3_key)
      
      {
        file_size: object_info.content_length,
        content_type: object_info.content_type,
        etag: object_info.etag,
        last_modified: object_info.last_modified,
        file_extension: File.extname(s3_key).downcase
      }
    rescue => e
      puts "Error extracting file info: #{e.message}"
      {
        file_size: nil,
        content_type: 'application/octet-stream',
        etag: nil,
        last_modified: nil,
        file_extension: File.extname(s3_key).downcase
      }
    end
  end
end
