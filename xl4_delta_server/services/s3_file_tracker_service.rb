require_relative '../models/s3_file_tracking'

class S3FileTrackerService
  def self.find_file_by_fingerprint(fingerprint)
    S3FileTracking.find_latest_by_fingerprint(fingerprint)
  end

  def self.find_files_in_bucket(bucket_name, limit: 100)
    S3FileTracking.by_bucket(bucket_name)
                  .recent
                  .limit(limit)
  end

  def self.find_similar_files(fingerprint, exclude_id: nil)
    query = S3FileTracking.by_fingerprint(fingerprint)
    query = query.where.not(id: exclude_id) if exclude_id
    query.recent
  end

  def self.get_file_history(bucket, key)
    S3FileTracking.where(s3_bucket: bucket, s3_key: key)
                  .order(created_at: :desc)
  end

  def self.cleanup_old_delete_records(days_old: 30)
    cutoff_date = days_old.days.ago
    deleted_count = S3FileTracking.where(event_type: 'DELETE')
                                  .where('created_at < ?', cutoff_date)
                                  .delete_all
    
    puts "Cleaned up #{deleted_count} old delete records"
    deleted_count
  end

  def self.get_bucket_statistics(bucket_name)
    stats = S3FileTracking.by_bucket(bucket_name)
                          .group(:event_type)
                          .count

    total_files = S3FileTracking.by_bucket(bucket_name)
                                .where(event_type: ['CREATE', 'UPDATE'])
                                .count

    total_size = S3FileTracking.by_bucket(bucket_name)
                               .where(event_type: ['CREATE', 'UPDATE'])
                               .sum(:file_size) || 0

    {
      bucket: bucket_name,
      total_files: total_files,
      total_size_bytes: total_size,
      total_size_human: human_readable_size(total_size),
      event_counts: stats,
      last_activity: S3FileTracking.by_bucket(bucket_name).recent.first&.created_at
    }
  end

  def self.find_duplicate_files(bucket_name = nil)
    query = S3FileTracking.where.not(fingerprint: nil)
    query = query.by_bucket(bucket_name) if bucket_name

    # Group by fingerprint and find those with multiple entries
    duplicates = query.group(:fingerprint)
                      .having('COUNT(*) > 1')
                      .count

    duplicate_details = []
    duplicates.each do |fingerprint, count|
      files = S3FileTracking.by_fingerprint(fingerprint)
                            .where(event_type: ['CREATE', 'UPDATE'])
                            .recent

      duplicate_details << {
        fingerprint: fingerprint,
        count: count,
        files: files.map do |file|
          {
            id: file.id,
            s3_url: file.s3_url,
            file_size: file.file_size,
            created_at: file.created_at
          }
        end
      }
    end

    duplicate_details
  end

  def self.search_files(query_params = {})
    scope = S3FileTracking.all

    if query_params[:bucket]
      scope = scope.by_bucket(query_params[:bucket])
    end

    if query_params[:event_type]
      scope = scope.by_event_type(query_params[:event_type])
    end

    if query_params[:file_extension]
      scope = scope.where(file_extension: query_params[:file_extension])
    end

    if query_params[:min_size]
      scope = scope.where('file_size >= ?', query_params[:min_size])
    end

    if query_params[:max_size]
      scope = scope.where('file_size <= ?', query_params[:max_size])
    end

    if query_params[:created_after]
      scope = scope.where('created_at >= ?', query_params[:created_after])
    end

    if query_params[:created_before]
      scope = scope.where('created_at <= ?', query_params[:created_before])
    end

    if query_params[:key_pattern]
      scope = scope.where('s3_key ILIKE ?', "%#{query_params[:key_pattern]}%")
    end

    scope.recent.limit(query_params[:limit] || 100)
  end

  private

  def self.human_readable_size(size_bytes)
    return 'Unknown' unless size_bytes

    units = %w[B KB MB GB TB]
    size = size_bytes.to_f
    unit_index = 0

    while size >= 1024 && unit_index < units.length - 1
      size /= 1024.0
      unit_index += 1
    end

    "#{size.round(2)} #{units[unit_index]}"
  end
end
