class EcsJobMetadata < ActiveRecord::Base
  belongs_to :ecs_job

  # Scopes for Android fingerprint queries
  scope :with_base_fingerprint, ->(fingerprint) { where(base_android_fingerprint: fingerprint) }
  scope :with_target_fingerprint, ->(fingerprint) { where(target_android_fingerprint: fingerprint) }
  scope :with_matching_fingerprints, -> { where('base_android_fingerprint = target_android_fingerprint') }
  scope :with_different_fingerprints, -> { where('base_android_fingerprint != target_android_fingerprint') }

  def android_fingerprints_match?
    base_android_fingerprint.present? &&
    target_android_fingerprint.present? &&
    base_android_fingerprint == target_android_fingerprint
  end

  def has_android_fingerprints?
    base_android_fingerprint.present? || target_android_fingerprint.present?
  end
end