class S3FileTracking < ActiveRecord::Base
  validates :s3_bucket, presence: true
  validates :s3_key, presence: true
  validates :s3_url, presence: true
  validates :event_type, presence: true, inclusion: { in: %w[CREATE UPDATE DELETE] }

  scope :by_bucket, ->(bucket) { where(s3_bucket: bucket) }
  scope :by_fingerprint, ->(fingerprint) { where(fingerprint: fingerprint) }
  scope :recent, -> { order(created_at: :desc) }
  scope :by_event_type, ->(event_type) { where(event_type: event_type) }

  def self.find_by_s3_location(bucket, key)
    find_by(s3_bucket: bucket, s3_key: key)
  end

  def self.find_latest_by_fingerprint(fingerprint)
    by_fingerprint(fingerprint).recent.first
  end

  def s3_path
    "s3://#{s3_bucket}/#{s3_key}"
  end

  def file_name
    File.basename(s3_key)
  end

  def directory_path
    File.dirname(s3_key)
  end

  def human_readable_size
    return 'Unknown' unless file_size

    units = %w[B KB MB GB TB]
    size = file_size.to_f
    unit_index = 0

    while size >= 1024 && unit_index < units.length - 1
      size /= 1024.0
      unit_index += 1
    end

    "#{size.round(2)} #{units[unit_index]}"
  end
end
