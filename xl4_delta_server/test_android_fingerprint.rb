#!/usr/bin/env ruby

# Test script for Android fingerprint extraction
# This script tests the AndroidFingerprintService with sample data

require_relative 'services/android_fingerprint_service'

# Test build.prop content
test_build_prop = <<~BUILD_PROP
  # Build properties
  ro.product.name=aosp_arm64
  ro.product.device=generic_arm64
  ro.product.brand=Android
  ro.product.model=AOSP on ARM64
  ro.system.build.fingerprint=Android/aosp_arm64/generic_arm64:VanillaIceCream/AP3A.240905.015.A2/eng.root.00000000.000000:eng/test-keys
  ro.build.version.release=VanillaIceCream
  ro.build.version.sdk=35
BUILD_PROP

puts "Testing Android fingerprint extraction..."
puts "=" * 50

# Test build.prop parsing
puts "\n1. Testing build.prop parsing:"
fingerprint = AndroidFingerprintService.send(:extract_fingerprint_from_build_prop, test_build_prop)
puts "Extracted fingerprint: #{fingerprint}"

expected = "Android/aosp_arm64/generic_arm64:VanillaIceCream/AP3A.240905.015.A2/eng.root.00000000.000000:eng/test-keys"
if fingerprint == expected
  puts "✅ Build.prop parsing test PASSED"
else
  puts "❌ Build.prop parsing test FAILED"
  puts "Expected: #{expected}"
  puts "Got: #{fingerprint}"
end

puts "\n2. Testing with missing fingerprint:"
empty_build_prop = <<~BUILD_PROP
  ro.product.name=aosp_arm64
  ro.product.device=generic_arm64
BUILD_PROP

fingerprint = AndroidFingerprintService.send(:extract_fingerprint_from_build_prop, empty_build_prop)
if fingerprint.nil?
  puts "✅ Missing fingerprint test PASSED (correctly returned nil)"
else
  puts "❌ Missing fingerprint test FAILED (should return nil)"
end

puts "\n3. Testing file type detection:"
test_cases = [
  { file: "test.apk", expected: :apk },
  { file: "test.zip", expected: :zip },
  { file: "system.img", expected: :img },
  { file: "unknown.txt", expected: :unsupported }
]

test_cases.each do |test_case|
  puts "  File: #{test_case[:file]}"
  # This would normally call extract_fingerprint_from_file, but we'll just test the logic
  extension = File.extname(test_case[:file]).downcase
  case extension
  when '.apk'
    result = :apk
  when '.zip'
    result = :zip
  when '.img'
    result = :img
  else
    result = :unsupported
  end
  
  if result == test_case[:expected]
    puts "    ✅ PASSED"
  else
    puts "    ❌ FAILED (expected #{test_case[:expected]}, got #{result})"
  end
end

puts "\n" + "=" * 50
puts "Android fingerprint extraction test completed!"
puts "\nTo test with real S3 files, use:"
puts "AndroidFingerprintService.extract_fingerprint_from_s3_file('s3://bucket/file.apk')"
