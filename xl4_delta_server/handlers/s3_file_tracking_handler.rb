require_relative '../lib/ecs_handler'
require_relative '../services/s3_file_tracker_service'

class S3FileTrackingHandler < ECSHandler
  def handle
    case @request_body['action']
    when 'search'
      handle_search
    when 'find_by_fingerprint'
      handle_find_by_fingerprint
    when 'bucket_stats'
      handle_bucket_stats
    when 'find_duplicates'
      handle_find_duplicates
    when 'file_history'
      handle_file_history
    when 'cleanup'
      handle_cleanup
    else
      error_response("Unknown action: #{@request_body['action']}", 400)
    end
  end

  private

  def handle_search
    puts "Handling S3 file tracking search request"
    
    begin
      search_params = @request_body['params'] || {}
      results = S3FileTrackerService.search_files(search_params)
      
      response_data = {
        total_results: results.count,
        files: results.map do |file|
          {
            id: file.id,
            s3_url: file.s3_url,
            fingerprint: file.fingerprint,
            event_type: file.event_type,
            file_size: file.file_size,
            file_size_human: file.human_readable_size,
            content_type: file.content_type,
            file_extension: file.file_extension,
            created_at: file.created_at,
            updated_at: file.updated_at
          }
        end
      }
      
      success_response(nil, response_data)
    rescue => e
      puts "Error in S3 file search: #{e.message}"
      error_response("Search failed: #{e.message}")
    end
  end

  def handle_find_by_fingerprint
    puts "Handling find by fingerprint request"
    
    fingerprint = @request_body['fingerprint']
    return error_response("Fingerprint is required", 400) unless fingerprint
    
    begin
      file = S3FileTrackerService.find_file_by_fingerprint(fingerprint)
      
      if file
        response_data = {
          id: file.id,
          s3_url: file.s3_url,
          fingerprint: file.fingerprint,
          event_type: file.event_type,
          file_size: file.file_size,
          file_size_human: file.human_readable_size,
          content_type: file.content_type,
          file_extension: file.file_extension,
          created_at: file.created_at,
          updated_at: file.updated_at
        }
        success_response(nil, response_data)
      else
        error_response("File not found with fingerprint: #{fingerprint}", 404)
      end
    rescue => e
      puts "Error finding file by fingerprint: #{e.message}"
      error_response("Search failed: #{e.message}")
    end
  end

  def handle_bucket_stats
    puts "Handling bucket statistics request"
    
    bucket_name = @request_body['bucket']
    return error_response("Bucket name is required", 400) unless bucket_name
    
    begin
      stats = S3FileTrackerService.get_bucket_statistics(bucket_name)
      success_response(nil, stats)
    rescue => e
      puts "Error getting bucket statistics: #{e.message}"
      error_response("Statistics retrieval failed: #{e.message}")
    end
  end

  def handle_find_duplicates
    puts "Handling find duplicates request"
    
    begin
      bucket_name = @request_body['bucket'] # Optional
      duplicates = S3FileTrackerService.find_duplicate_files(bucket_name)
      
      response_data = {
        total_duplicate_groups: duplicates.count,
        duplicates: duplicates
      }
      
      success_response(nil, response_data)
    rescue => e
      puts "Error finding duplicates: #{e.message}"
      error_response("Duplicate search failed: #{e.message}")
    end
  end

  def handle_file_history
    puts "Handling file history request"
    
    bucket = @request_body['bucket']
    key = @request_body['key']
    
    return error_response("Bucket and key are required", 400) unless bucket && key
    
    begin
      history = S3FileTrackerService.get_file_history(bucket, key)
      
      response_data = {
        bucket: bucket,
        key: key,
        history_count: history.count,
        history: history.map do |record|
          {
            id: record.id,
            event_type: record.event_type,
            fingerprint: record.fingerprint,
            file_size: record.file_size,
            created_at: record.created_at
          }
        end
      }
      
      success_response(nil, response_data)
    rescue => e
      puts "Error getting file history: #{e.message}"
      error_response("History retrieval failed: #{e.message}")
    end
  end

  def handle_cleanup
    puts "Handling cleanup request"
    
    begin
      days_old = @request_body['days_old'] || 30
      deleted_count = S3FileTrackerService.cleanup_old_delete_records(days_old: days_old)
      
      response_data = {
        deleted_records: deleted_count,
        days_old: days_old
      }
      
      success_response(nil, response_data)
    rescue => e
      puts "Error during cleanup: #{e.message}"
      error_response("Cleanup failed: #{e.message}")
    end
  end

  def success_response(task_id, data)
    {
      statusCode: 200,
      headers: { "Content-Type" => "application/json" },
      body: {
        success: true,
        data: data
      }.to_json
    }
  end
end
