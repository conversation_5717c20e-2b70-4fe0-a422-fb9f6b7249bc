require 'sinatra'
require 'json'
require 'sinatra/activerecord'
require 'time'
require 'jwt'
require 'openssl'
require 'nokogiri'
require 'aws-sdk-ecs'
require 'active_record'
require 'aws-sdk-s3'
require 'httparty'
require 'tempfile'
require 'digest'

# Load models
require_relative 'models/ecs_job.rb'
require_relative 'models/ecs_job_result.rb'
require_relative 'models/ecs_job_metadata.rb'
require_relative 'models/s3_file_tracking.rb'

# Load application components
require_relative 'lib/json_parser'
require_relative 'lib/ecs_request_handler'

ActiveRecord::Base.establish_connection(
  adapter: ENV['DB_ADAPTER'],
  host: <PERSON><PERSON><PERSON>['DB_HOST'],
  port: EN<PERSON>['DB_PORT'],
  username: <PERSON><PERSON><PERSON>['DB_USER_NAME'],
  password: ENV['DB_PASSWORD'],
  database: ENV['DB_NAME']
)

def lambda_handler(event:, context:)
  puts "Received EVENT POST request with data: #{event['body']}"
  request_body = event['body'] ? JsonParser.parse(event['body']) : {}
  handler = ECSRequestHandler.create_handler(event['path'], request_body)
  if handler
    handler.handle
  else
    puts "Unknown path: #{event['path']}"
    { statusCode: 404, headers: { "Content-Type" => "application/json" }, body: { message: "Path not found" }.to_json }
  end
end