require 'sinatra'
require 'json'
require 'sinatra/activerecord'
require 'time'
require 'jwt'
require 'openssl'
require 'nokogiri'
require 'aws-sdk-ecs'
require 'active_record'
require 'aws-sdk-s3'
require 'httparty'
require 'tempfile'
require 'digest'
require_relative 'models/ecs_job.rb'
require_relative 'models/ecs_job_result.rb'
require_relative 'models/ecs_job_metadata.rb'

ActiveRecord::Base.establish_connection(
  adapter: ENV['DB_ADAPTER'],
  host: <PERSON><PERSON><PERSON>['DB_HOST'],
  port: EN<PERSON>['DB_PORT'],
  username: <PERSON><PERSON><PERSON>['DB_USER_NAME'],
  password: <PERSON>N<PERSON>['DB_PASSWORD'],
  database: ENV['DB_NAME']
)

class ECSHandler
  def initialize(request_body)
    @ecs = Aws::ECS::Client.new(region: ENV['AWS_DEFAULT_REGION'])
    @cluster_name = ENV['ECS_CLUSTER_NAME']
    @request_body = request_body
  end

  protected

  # Common ECS task configuration
  def ecs_task_config
    {
      task_definition: ENV['ECS_TASK_DEFINITION_SCALE2'],
      container_name: 'DeltaExecutorScale2',
      subnets: ENV['ECS_SUBNET_IDS'].split(','),
      security_groups: ENV['ECS_SECURITY_GROUP_IDS'].split(','),
      assign_public_ip: 'DISABLED'
    }
  end

  # Common response formatting
  def success_response(task_id, message = 'Delta process initiated')
    {
      statusCode: 200,
      headers: { "Content-Type" => "application/json" },
      body: { id: task_id, message: message }.to_json
    }
  end

  def error_response(message, status_code = 500)
    {
      statusCode: status_code,
      headers: { "Content-Type" => "application/json" },
      body: { message: message }.to_json
    }
  end

  # Common job checking logic
  def check_existing_job(compatibility, src_path, dest_path)
    EcsJobMetadata.where(
      compatibility: compatibility,
      src_path: src_path,
      dest_path: dest_path,
      delta_size: nil
    ).last
  end

  def existing_job_response(existing_job)
    puts "Return data from the existing job #{existing_job.inspect} and task #{existing_job.ecs_job.task}}"
    success_response(existing_job.ecs_job.task, 'Delta process already running')
  end

  # Common S3 size calculation
  def get_s3_size(region, s3_path)
    begin
      # Handle both S3 URLs and regular URLs
      if s3_path.start_with?('s3://')
        s3_client = Aws::S3::Client.new(region: region, http_wire_trace: true)
        uri = URI.parse(s3_path)
        bucket_name = uri.host
        object_key = uri.path[1..-1] # Remove leading slash
        puts "Parsed S3 URI - Bucket: #{bucket_name}, Key: #{object_key}, Region: #{region}"
        response = s3_client.head_object(bucket: bucket_name, key: object_key)
        puts "Object Size: #{response.content_length} bytes"
        response.content_length
      else
        # For regular URLs, get size via HTTP HEAD request
        puts "Getting size for regular URL: #{s3_path}"
        response = HTTParty.head(s3_path)
        if response.success? && response.headers['content-length']
          size = response.headers['content-length'].to_i
          puts "URL Content Size: #{size} bytes"
          size
        else
          puts "Could not determine size for URL: #{s3_path}"
          nil
        end
      end
    rescue URI::InvalidURIError
      puts "Invalid URI: #{s3_path}"
      nil
    rescue Aws::S3::Errors::NoSuchBucket
      puts "Bucket not found: #{bucket_name}"
      nil
    rescue Aws::S3::Errors::NoSuchKey
      puts "Object not found: #{object_key}"
      nil
    rescue Aws::S3::Errors::ServiceError => e
      puts "AWS S3 error: #{e.message}"
      nil
    rescue => e
      puts "Error getting size for #{s3_path}: #{e.message}"
      nil
    end
  end

  # Common ECS task execution
  def run_ecs_task(environment_vars)
    config = ecs_task_config

    response = @ecs.run_task({
      cluster: @cluster_name,
      launch_type: 'FARGATE',
      task_definition: config[:task_definition],
      network_configuration: {
        awsvpc_configuration: {
          subnets: config[:subnets],
          security_groups: config[:security_groups],
          assign_public_ip: config[:assign_public_ip]
        }
      },
      count: 1,
      overrides: {
        container_overrides: [
          {
            name: config[:container_name],
            environment: environment_vars
          }
        ]
      }
    })

    task_arn = response.tasks[0].task_arn
    task_arn.split('/').last
  end

  # Common job creation and metadata setup
  def create_job_with_metadata(task_id, compatibility, src_path, dest_path, delta_path)
    ecs_job = EcsJob.create(task: task_id)
    src_size = get_s3_size(ENV['AWS_DEFAULT_REGION'], src_path)
    dest_size = get_s3_size(ENV['AWS_DEFAULT_REGION'], dest_path)
    ecs_job.ecs_job_metadatas.create(
      compatibility: compatibility,
      src_path: src_path,
      src_size: src_size,
      dest_path: dest_path,
      dest_size: dest_size,
      delta_path: delta_path
    )
    ecs_job
  end

  # Common job lookup methods
  def find_job_by_token(token)
    EcsJob.find_by(task: token)
  end

  def get_latest_job_result(ecs_job)
    ecs_job.ecs_job_results.order(updated_at: :desc).first
  end

  # Common status processing
  def processed_status(status)
    case status
    when 'PENDING'
      'RUNNING'
    when 'RUNNING'
      'RUNNING'
    when 'STOPPED'
      'COMPLETED'
    end
  end
end

class DeltaExecute2Handler < ECSHandler
  def handle
    puts "Handling request for /v1/delta/execute2"

    base_url = extract_url_from_binary_reference(@request_body['base'])
    target_url = extract_url_from_binary_reference(@request_body['target'])
    store_url = extract_url_from_storage_location(@request_body['store'])

    existing_job = check_existing_job(@request_body['capabilities'], base_url, target_url)
    return existing_job_response(existing_job) if existing_job

    puts "Base URL: #{base_url}, Target URL: #{target_url}, Store URL: #{store_url}"
    puts "Capabilities: #{@request_body['capabilities']}"
    
    begin
      environment_vars = [
        { name: 'AWS_DEFAULT_REGION', value: ENV['AWS_DEFAULT_REGION'] },
        { name: 'CAP_STRING', value: @request_body['capabilities'] },
        { name: 'DELTA_S3_PATH', value: store_url },
        { name: 'DEST_S3_PATH', value: target_url },
        { name: 'SRC_S3_PATH', value: base_url }
      ]

      task_id = run_ecs_task(environment_vars)
      puts "ECS task started successfully with id: #{task_id}"

      create_job_with_metadata(task_id, @request_body['capabilities'], base_url, target_url, store_url)
      success_response(task_id)
    rescue Aws::ECS::Errors::ServiceError => e
      puts "Failed to run ECS task: #{e.message}"
      error_response("Delta process failed to start: #{e.message}")
    end
  end

  private

  def extract_url_from_binary_reference(binary_ref)
    case binary_ref['_o']
    when 'URLBinaryReference'
      url = binary_ref['url']
      puts "Processing URLBinaryReference with URL: #{url}"

      if url.start_with?('s3://')
        puts "S3 URL detected, returning as-is: #{url}"
        url
      else
        puts "Regular URL detected, downloading and uploading to S3: #{url}"
        download_and_upload_to_s3(url)
      end
    when 'AndroidFingerprintBinaryReference'
      # For Android fingerprint, you might need to resolve this to an actual URL
      # This is a placeholder - implement based on your business logic
      "s3://android-binaries/#{binary_ref['fingerprint']}"
    else
      raise "Unsupported binary reference type: #{binary_ref['_o']}"
    end
  end

  def download_and_upload_to_s3(url)
    begin
      puts "Starting download from URL: #{url}"

      # Create a temporary file
      temp_file = Tempfile.new(['download', File.extname(url)])
      temp_file.binmode

      # Download the file using HTTParty
      response = HTTParty.get(url, stream_body: true) do |fragment|
        temp_file.write(fragment)
      end

      temp_file.close

      if response.success?
        puts "Download successful, file size: #{File.size(temp_file.path)} bytes"

        # Generate S3 key based on URL hash and timestamp
        url_hash = Digest::SHA256.hexdigest(url)[0..15]
        timestamp = Time.now.strftime('%Y%m%d_%H%M%S')
        file_extension = File.extname(url).empty? ? '' : File.extname(url)
        s3_key = "downloaded-binaries/#{timestamp}_#{url_hash}#{file_extension}"

        # Upload to S3
        s3_bucket = ENV['DOWNLOAD_S3_BUCKET'] || ENV['S3_BUCKET'] || 'default-bucket'
        s3_url = upload_file_to_s3(temp_file.path, s3_bucket, s3_key)

        puts "Upload successful, S3 URL: #{s3_url}"
        s3_url
      else
        raise "Failed to download file: HTTP #{response.code}"
      end
    rescue => e
      puts "Error downloading and uploading file: #{e.message}"
      raise "Failed to process URL download: #{e.message}"
    ensure
      temp_file&.unlink # Clean up temporary file
    end
  end

  def upload_file_to_s3(file_path, bucket, key)
    s3_client = Aws::S3::Client.new(region: ENV['AWS_DEFAULT_REGION'])

    File.open(file_path, 'rb') do |file|
      s3_client.put_object(
        bucket: bucket,
        key: key,
        body: file,
        content_type: determine_content_type(file_path)
      )
    end

    "s3://#{bucket}/#{key}"
  end

  def determine_content_type(file_path)
    case File.extname(file_path).downcase
    when '.apk'
      'application/vnd.android.package-archive'
    when '.ipa'
      'application/octet-stream'
    when '.zip'
      'application/zip'
    when '.tar.gz', '.tgz'
      'application/gzip'
    else
      'application/octet-stream'
    end
  end

  def extract_url_from_storage_location(storage_loc)
    case storage_loc['_o']
    when 'S3ObjectStorageLocation'
      storage_loc['s3Url']
    else
      raise "Unsupported storage location type: #{storage_loc['_o']}"
    end
  end
end

class DeltaExecuteHandler < ECSHandler
  def handle
    puts "Handling request for /v1/delta/execute"

    existing_job = check_existing_job(@request_body['compatibility'], @request_body['src'], @request_body['dest'])
    return existing_job_response(existing_job) if existing_job

    begin
      environment_vars = [
        { name: 'AWS_DEFAULT_REGION', value: ENV['AWS_DEFAULT_REGION'] },
        { name: 'CAP_STRING', value: @request_body['compatibility'] },
        { name: 'DELTA_S3_PATH', value: @request_body['delta'] },
        { name: 'DEST_S3_PATH', value: @request_body['dest'] },
        { name: 'SRC_S3_PATH', value: @request_body['src'] }
      ]

      task_id = run_ecs_task(environment_vars)
      puts "ECS task started successfully with id: #{task_id}"

      create_job_with_metadata(task_id, @request_body['compatibility'], @request_body['src'], @request_body['dest'], @request_body['delta'])
      success_response(task_id)
    rescue Aws::ECS::Errors::ServiceError => e
      puts "Failed to run ECS task: #{e.message}"
      error_response("Delta process failed to start: #{e.message}")
    end
  end
end

class DeltaStatusHandler < ECSHandler
  def handle
    puts "Handling request for /v1/delta/status"
    ecs_job = find_job_by_token(@request_body['token'])
    result = get_latest_job_result(ecs_job)

    puts "Task Last status: #{processed_status(result.state)} exit_code #{result.exit_code}"

    {
      statusCode: 200,
      headers: { "Content-Type" => "application/json" },
      body: {
        status: processed_status(result.state),
        failure_message: result.message,
        exit_code: result.exit_code
      }.to_json
    }
  end

end

class DeltaSummaryHandler < ECSHandler
  def handle
    puts "Handling request for /v1/delta/summary"
    ecs_job = find_job_by_token(@request_body['token'])
    result = get_latest_job_result(ecs_job)
    ecs_job_metadata = ecs_job.ecs_job_metadatas.last

    puts "Task Last status: #{processed_status(result.state)} exit_code #{result.exit_code}"

    {
      statusCode: 200,
      headers: { "Content-Type" => "application/json" },
      body: build_summary_response(ecs_job_metadata, result).to_json
    }
  end

  private

  def build_summary_response(metadata, result)
    {
      compatibility: metadata.compatibility,
      src_path: metadata.src_path,
      src_size: metadata.src_size,
      dest_path: metadata.dest_path,
      dest_size: metadata.dest_size,
      delta_path: metadata.delta_path,
      delta_size: metadata.delta_size,
      delta_percentage: metadata.delta_percentage,
      started_at: metadata.created_at,
      ended_at: metadata.updated_at,
      status: processed_status(result.state),
      failure_message: result.message,
      exit_code: result.exit_code
    }
  end
end

class ECSRequestHandler
  def self.create_handler(path, request_body)
    case path
    when '/v1/delta/execute'
      DeltaExecuteHandler.new(request_body)
    when '/v1/delta/execute2'
      DeltaExecute2Handler.new(request_body)
    when '/v1/delta/status'
      DeltaStatusHandler.new(request_body)
    when '/v1/delta/summary'
      DeltaSummaryHandler.new(request_body)      
    else
      nil
    end
  end
end



def parse_json(data)
  JSON.parse(data)
rescue JSON::ParserError
  puts "JSON Parse Error"
  nil
end

def lambda_handler(event:, context:)
  puts EcsJob.all.count

  puts "Received EVENT POST request with data: #{event['body']}"
  request_body = event['body'] ? parse_json(event['body']) : {}
  
  handler = ECSRequestHandler.create_handler(event['path'], request_body)

  if handler
    handler.handle
  else
    puts "Unknown path: #{event['path']}"
    { statusCode: 404, headers: { "Content-Type" => "application/json" }, body: { message: "Path not found" }.to_json }
  end
end