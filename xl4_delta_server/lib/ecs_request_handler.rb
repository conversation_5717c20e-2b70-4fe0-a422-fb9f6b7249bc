require_relative '../handlers/delta_execute_handler'
require_relative '../handlers/delta_execute2_handler'
require_relative '../handlers/delta_status_handler'
require_relative '../handlers/delta_summary_handler'
require_relative '../handlers/s3_file_tracking_handler'

class ECSRequestHandler
  def self.create_handler(path, request_body)
    case path
    when '/v1/delta/execute'
      DeltaExecuteHandler.new(request_body)
    when '/v1/delta/execute2'
      DeltaExecute2Handler.new(request_body)
    when '/v1/delta/status'
      DeltaStatusHandler.new(request_body)
    when '/v1/delta/summary'
      DeltaSummaryHandler.new(request_body)
    when '/v1/s3/files'
      S3FileTrackingHandler.new(request_body)
    else
      nil
    end
  end
end
