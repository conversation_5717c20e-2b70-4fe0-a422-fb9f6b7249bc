# XL4 Delta Server SAM

A serverless application for binary delta processing with S3 file tracking, built using AWS SAM (Serverless Application Model).

## 🏗️ Architecture

The application consists of multiple Lambda functions with different trigger mechanisms:

### Lambda Functions

- **`xl4_delta_server`** - Main delta processing API (API Gateway triggered)
- **`xl4_delta_event_recorder`** - ECS task event processing (EventBridge triggered)
- **`xl4_s3_file_tracker`** - S3 file operation tracking (S3 Event triggered from configured bucket)

### Trigger Types

- **API Gateway** → `xl4_delta_server` (REST API endpoints)
- **EventBridge/CloudWatch Events** → `xl4_delta_event_recorder` (ECS task events)
- **S3 Bucket Events** → `xl4_s3_file_tracker` (file operations)

## 📋 Prerequisites

- AWS CLI configured
- SAM CLI installed
- Docker installed
- PostgreSQL database
- Ruby 3.1.3+
- Bundler gem

## 🚀 Quick Start

### 1. Environment Setup

Copy the environment template and configure your settings:

```bash
cp .env.example .env
# Edit .env with your database and AWS configuration
```

Required environment variables:
```bash
# Database
DB_ADAPTER=postgresql
DB_HOST=localhost
DB_PORT=5432
DB_USER_NAME=your_username
DB_PASSWORD=your_password
DB_NAME=xl4_delta_server_development

# AWS
AWS_DEFAULT_REGION=us-east-1
ECS_CLUSTER_NAME=your-ecs-cluster
ECS_TASK_DEFINITION_SCALE2=your-task-definition
ECS_SUBNET_IDS=subnet-12345,subnet-67890
ECS_SECURITY_GROUP_IDS=sg-12345,sg-67890

# S3
S3_BUCKET=your-default-bucket
DOWNLOAD_S3_BUCKET=your-download-bucket
S3_TRACKING_BUCKET=your-tracking-bucket
```

### 2. Database Setup

#### Option A: Automated Setup (Recommended)
```bash
# Make setup script executable and run
chmod +x scripts/setup_database.sh
./scripts/setup_database.sh
```

#### Option B: Manual Setup
```bash
# Install dependencies
bundle install

# Create and migrate database
rake db:create
rake db:migrate

# Check migration status
rake db:status
```

### 3. Build and Deploy

```bash
# Build the application
sam build --config-env deltaserverguruqa

# Deploy to AWS
sam deploy --config-env deltaserverguruqa --no-confirm-changeset
```

## 🗄️ Database Schema

The application uses 4 main tables:

### Core Tables
1. **`ecs_jobs`** - ECS task tracking
2. **`ecs_job_results`** - Task execution results
3. **`ecs_job_metadatas`** - Delta processing metadata
4. **`s3_file_trackings`** - S3 file operations with fingerprints

### Migration Commands

```bash
# Check migration status
rake db:status

# Run all migrations
rake db:migrate

# Rollback last migration
rake db:rollback

# Reset database (drop, create, migrate)
rake db:reset

# Database setup script
./scripts/setup_database.sh
```

## 🔧 Database Management

### Available Rake Tasks

```bash
rake db:create     # Create database
rake db:drop       # Drop database
rake db:migrate    # Run migrations
rake db:rollback   # Rollback migrations
rake db:status     # Show migration status
rake db:reset      # Reset database
rake db:setup      # Create and migrate
```

### Setup Script Options

```bash
./scripts/setup_database.sh setup    # Full setup (default)
./scripts/setup_database.sh reset    # Reset database
./scripts/setup_database.sh status   # Show status
./scripts/setup_database.sh help     # Show help
```

## 📡 API Endpoints

### Delta Processing

#### Execute Delta (v1)
```bash
curl -X POST https://your-api-gateway/v1/delta/execute \
  -H "Content-Type: application/json" \
  -d '{
    "src": "s3://bucket/source-file",
    "dest": "s3://bucket/target-file",
    "compatibility": "A:3;B:3;C:100;E:2.6",
    "delta": "s3://bucket/delta-file"
  }'
```

#### Execute Delta (v2) - Enhanced with URL Support
```bash
curl -X POST https://your-api-gateway/v1/delta/execute2 \
  -H "Content-Type: application/json" \
  -d '{
    "capabilities": "A:3;B:3;C:100;E:2.6",
    "base": {
      "_o": "URLBinaryReference",
      "url": "s3://bucket/source-file"
    },
    "target": {
      "_o": "URLBinaryReference",
      "url": "https://example.com/target-file.apk"
    },
    "store": {
      "_o": "S3ObjectStorageLocation",
      "s3Url": "s3://bucket/delta-output"
    }
  }'
```

#### Check Status
```bash
curl -X POST https://your-api-gateway/v1/delta/status \
  -H "Content-Type: application/json" \
  -d '{"token": "task-id"}'
```

#### Get Summary
```bash
curl -X POST https://your-api-gateway/v1/delta/summary \
  -H "Content-Type: application/json" \
  -d '{"token": "task-id"}'
```

### S3 File Tracking

> **Note**: These endpoints are served by `xl4_delta_server`, not `xl4_s3_file_tracker`. The S3 file tracker only processes events in the background.

#### Search Files
```bash
curl -X POST https://your-api-gateway/v1/s3/files \
  -H "Content-Type: application/json" \
  -d '{
    "action": "search",
    "params": {
      "bucket": "my-bucket",
      "event_type": "CREATE",
      "file_extension": ".apk",
      "limit": 50
    }
  }'
```

#### Find by Fingerprint
```bash
curl -X POST https://your-api-gateway/v1/s3/files \
  -H "Content-Type: application/json" \
  -d '{
    "action": "find_by_fingerprint",
    "fingerprint": "abc123def456..."
  }'
```

#### Bucket Statistics
```bash
curl -X POST https://your-api-gateway/v1/s3/files \
  -H "Content-Type: application/json" \
  -d '{
    "action": "bucket_stats",
    "bucket": "my-bucket"
  }'
```

## 🔄 S3 File Tracking Setup

The `xl4_s3_file_tracker` is an **event-driven Lambda function** that automatically processes S3 bucket events from a **single configured bucket**.

### 1. Configure Environment Variable

Set the bucket to track in your Lambda environment:

```bash
S3_TRACKING_BUCKET=your-specific-bucket-name
```

### 2. Configure S3 Event Notifications

Set up **only the configured bucket** to trigger the file tracker Lambda:

```json
{
  "Rules": [
    {
      "Name": "FileTrackingRule",
      "Status": "Enabled",
      "Filter": {"Prefix": ""},
      "LambdaConfigurations": [
        {
          "Id": "FileTracker",
          "LambdaFunctionArn": "arn:aws:lambda:region:account:function:xl4-s3-file-tracker",
          "Events": ["s3:ObjectCreated:*", "s3:ObjectRemoved:*"]
        }
      ]
    }
  ]
}
```

> **Important**: Only configure the bucket specified in `S3_TRACKING_BUCKET`. Events from other buckets will be ignored.

### 3. Event Processing Flow

1. **File Operation** → User uploads/deletes file in the configured S3 bucket
2. **S3 Event** → S3 generates event and sends to Lambda
3. **Bucket Validation** → Lambda checks if event is from configured bucket
4. **Event Processing** → If valid, `xl4_s3_file_tracker` processes the event
5. **Fingerprint Generated** → Creates unique file fingerprint
6. **Database Updated** → Stores file metadata in `s3_file_trackings` table

### 4. Lambda Permissions

Ensure the Lambda has permissions to:
- Read from the configured S3 bucket
- Write to the database
- Generate CloudWatch logs

## 🏗️ Project Structure

```
xl4-delta-server-sam/
├── db/
│   ├── migrate/                    # Database migrations
│   └── README.md                   # Database documentation
├── xl4_delta_server/               # Main API Lambda
│   ├── handlers/                   # Request handlers
│   ├── lib/                        # Core libraries
│   ├── models/                     # ActiveRecord models
│   ├── services/                   # Business logic services
│   └── app.rb                      # Lambda entry point
├── xl4_delta_event_recorder/       # ECS event processor
│   └── app.rb                      # Lambda entry point
├── xl4_s3_file_tracker/           # S3 file tracker
│   ├── app.rb                      # Lambda entry point
│   └── README.md                   # S3 tracker documentation
├── scripts/
│   └── setup_database.sh          # Database setup script
├── .env.example                    # Environment template
├── Rakefile                        # Database tasks
└── template.yaml                   # SAM template
```

## 🔧 Development

### Local Development

```bash
# Install dependencies
bundle install

# Set up database
./scripts/setup_database.sh

# Run tests (if available)
bundle exec rspec

# Local API testing
sam local start-api
```

### Adding New Migrations

```bash
# Create new migration
touch db/migrate/$(date +%Y%m%d%H%M%S)_your_migration_name.rb

# Edit the migration file
# Run migration
rake db:migrate
```

## 🚨 Troubleshooting

### Database Issues

```bash
# Check connection
rake db:status

# Reset database
./scripts/setup_database.sh reset

# Manual connection test
psql -h $DB_HOST -U $DB_USER_NAME -d $DB_NAME
```

### Lambda Issues

```bash
# Check logs
sam logs -n YourFunctionName --tail

# Local testing
sam local invoke YourFunctionName -e events/test-event.json
```

### S3 Event Issues

- Verify S3 bucket event configuration
- Check Lambda permissions
- Review CloudWatch logs for the S3 file tracker

## 📚 Additional Documentation

- [Database Setup Guide](db/README.md)
- [S3 File Tracker Guide](xl4_s3_file_tracker/README.md)
- [API Schema](schema/openapi.yaml)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Update documentation
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.