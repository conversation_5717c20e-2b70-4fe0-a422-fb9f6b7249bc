```
-- Create the ecs_jobs table
CREATE TABLE ecs_jobs (
    id SERIAL PRIMARY KEY,
    task VA<PERSON>HA<PERSON>,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create the ecs_job_results table with a foreign key to ecs_jobs
CREATE TABLE ecs_job_results (
    id SERIAL PRIMARY KEY,
    state VARCHAR,
    message VARCHAR,
    exit_code INTEGER,
    ecs_job_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ecs_job_id) REFERENCES ecs_jobs(id) ON DELETE CASCADE
);

CREATE TABLE ecs_job_metadata (
    id SERIAL PRIMARY KEY,
    compatibility VARCHAR,
    src_path VARCHAR,
    src_size BIGINT,
    dest_path VARCHAR,
    dest_size BIGINT,
    delta_path VARCHAR,
    delta_size BIGINT,
    delta_percentage INTEGER,
    ecs_job_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ecs_job_id) REFERENCES ecs_jobs(id) ON DELETE CASCADE
);
```


```
sam build --config-env deltaserverguruqa
```

```
sam deploy --config-env deltaserverguruqa --no-confirm-changeset
```