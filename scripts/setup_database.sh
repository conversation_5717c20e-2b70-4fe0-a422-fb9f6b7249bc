#!/bin/bash

# XL4 Delta Server Database Setup Script
# This script sets up the database and runs all migrations

set -e  # Exit on any error

echo "🚀 XL4 Delta Server Database Setup"
echo "=================================="

# Check if required environment variables are set
check_env_vars() {
    echo "📋 Checking environment variables..."
    
    required_vars=("DB_ADAPTER" "DB_HOST" "DB_USER_NAME" "DB_PASSWORD" "DB_NAME")
    missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -ne 0 ]; then
        echo "❌ Missing required environment variables:"
        printf '   %s\n' "${missing_vars[@]}"
        echo ""
        echo "Please set the following environment variables:"
        echo "export DB_ADAPTER=postgresql"
        echo "export DB_HOST=localhost"
        echo "export DB_PORT=5432"
        echo "export DB_USER_NAME=your_username"
        echo "export DB_PASSWORD=your_password"
        echo "export DB_NAME=xl4_delta_server_development"
        exit 1
    fi
    
    echo "✅ All required environment variables are set"
}

# Check if PostgreSQL is running
check_postgresql() {
    echo "🔍 Checking PostgreSQL connection..."
    
    if ! pg_isready -h "$DB_HOST" -p "${DB_PORT:-5432}" -U "$DB_USER_NAME" > /dev/null 2>&1; then
        echo "❌ Cannot connect to PostgreSQL server"
        echo "   Host: $DB_HOST"
        echo "   Port: ${DB_PORT:-5432}"
        echo "   User: $DB_USER_NAME"
        echo ""
        echo "Please ensure PostgreSQL is running and accessible."
        exit 1
    fi
    
    echo "✅ PostgreSQL connection successful"
}

# Install Ruby dependencies
install_dependencies() {
    echo "📦 Installing Ruby dependencies..."
    
    if ! command -v bundle &> /dev/null; then
        echo "❌ Bundler not found. Please install bundler first:"
        echo "   gem install bundler"
        exit 1
    fi
    
    bundle install
    echo "✅ Dependencies installed"
}

# Create database
create_database() {
    echo "🗄️  Creating database..."
    
    if rake db:create 2>/dev/null; then
        echo "✅ Database created or already exists"
    else
        echo "❌ Failed to create database"
        exit 1
    fi
}

# Run migrations
run_migrations() {
    echo "🔄 Running database migrations..."
    
    if rake db:migrate; then
        echo "✅ Migrations completed successfully"
    else
        echo "❌ Migration failed"
        exit 1
    fi
}

# Show migration status
show_status() {
    echo "📊 Migration status:"
    echo ""
    rake db:status
}

# Main execution
main() {
    check_env_vars
    check_postgresql
    install_dependencies
    create_database
    run_migrations
    show_status
    
    echo ""
    echo "🎉 Database setup completed successfully!"
    echo ""
    echo "Next steps:"
    echo "1. Deploy the Lambda functions"
    echo "2. Configure S3 bucket event notifications"
    echo "3. Test the API endpoints"
}

# Handle script arguments
case "${1:-setup}" in
    "setup")
        main
        ;;
    "reset")
        echo "⚠️  This will DROP and recreate the database!"
        read -p "Are you sure? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            check_env_vars
            echo "🗑️  Resetting database..."
            rake db:reset
            show_status
            echo "✅ Database reset completed"
        else
            echo "❌ Database reset cancelled"
        fi
        ;;
    "status")
        check_env_vars
        show_status
        ;;
    "help")
        echo "Usage: $0 [command]"
        echo ""
        echo "Commands:"
        echo "  setup   - Set up database and run migrations (default)"
        echo "  reset   - Drop, recreate, and migrate database"
        echo "  status  - Show migration status"
        echo "  help    - Show this help message"
        ;;
    *)
        echo "Unknown command: $1"
        echo "Use '$0 help' for usage information"
        exit 1
        ;;
esac
